<?php

use App\Http\Controllers\ApplicantController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\WelcomeController;
use App\Livewire\PaymentForm;
use Illuminate\Support\Facades\Route;


Route::get('/', [WelcomeController::class, 'index']);


Route::prefix('applicant')->name('applicant.')->group(function () {
    Route::get('returning-applicant', [ApplicantController::class, 'index'])->name('returning-applicant');
    Route::get('recruitment-payment/{id}', [PaymentController::class, 'index'])->name('recruitment-payment');
    Route::post('payment', [PaymentController::class, 'initialize'])->name('payment.initialize');
    Route::get('payment/callback', [PaymentController::class, 'callback'])->name('payment.callback');
});

// Route::get('/payment', [PaymentController::class, 'index'])->name('payment.form');
// Route::post('/pay', [PaymentController::class, 'initialize'])->name('payment.initialize');
// Route::get('/payment/callback', [PaymentController::class, 'callback'])->name('payment.callback');

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Route::middleware('auth')->group(function () {
//     Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
//     Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
//     Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
// });

require __DIR__.'/auth.php';

Auth::routes();

