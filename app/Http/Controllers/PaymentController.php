<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Payment;
use Illuminate\Http\Request;
use App\Mail\TransactionEmail;
use App\Services\PaystackService;
use Illuminate\Support\Facades\Mail;

class PaymentController extends Controller
{
    protected $paystack;

    public function __construct(PaystackService $paystack)
    {
        $this->paystack = $paystack;
    }

    /**
     * Display a listing of the resource.
     */
    public function index($id)
    {
        return view("applicant.payment", compact('id'));
    }



    public function initialize(Request $request)
    {
        $request->validate([
            'name' => 'required|string',
            'email' => 'required|email',
            'phone' => 'required|string',
            'amount' => 'required|numeric|min:2000',
        ]);

        $data = [
            'email' => $request->email,
            'amount' => $request->amount * 100,
            'metadata' => [
                'name' => $request->name,
                'phone' => $request->phone
            ],
            'callback_url' => route('applicant.payment.callback'),
        ];

        $response = $this->paystack->initializeTransaction($data);
        return redirect($response['data']['authorization_url']);
    }

    public function callback(Request $request)
    {
        // TEMPORARY TESTING BLOCK - REMOVE LATER
        if (app()->environment('local') && !$request->has('reference')) {
            $mockResponse = [
                'status' => true,
                'data' => [
                    'status' => 'success',
                    'reference' => 'TEST_' . rand(1000, 9999),
                    'amount' => 10000,
                    'metadata' => $request->session()->get('payment_details', [
                        'name' => 'Test User',
                        'phone' => '0244022139'
                    ]),
                    'customer' => [
                        'email' => $request->session()->get('payment_details.email', '<EMAIL>')
                    ]
                ]
            ];

            $paymentDetails = $mockResponse['data'];
            return view('applicant.success', [
                'name' => $paymentDetails['metadata']['name'],
                'email' => $paymentDetails['customer']['email'],
                'phone' => $paymentDetails['metadata']['phone'],
                'amount' => $paymentDetails['amount'] / 100,
                'reference' => $paymentDetails['reference']
            ]);
        }
        // END TEMPORARY BLOCK

        $reference = $request->query('reference');
        $response = $this->paystack->verifyTransaction($reference);

        if ($response['status'] && $response['data']['status'] === 'success') {

            $paymentDetails = $response['data'];

            $password = $this->generateStrongPassword();

            User::create([
                'name' => $paymentDetails['metadata']['name'],
                'email' => $paymentDetails['customer']['email'],
                'password' => bcrypt($password),
            ]);

            // Email Data
            $mailData = [
                'name' => $paymentDetails['metadata']['name'],
                'email' => $paymentDetails['customer']['email'],
                'phone' => $paymentDetails['metadata']['phone'],
                'amount' => $paymentDetails['amount'] / 100,
                'reference' => $paymentDetails['reference'],
                'password' => $password
            ];

            Mail::to($paymentDetails['customer']['email'])->send(new TransactionEmail($mailData));

            return view('applicant.success', [
                'name' => $paymentDetails['metadata']['name'],
                'email' => $paymentDetails['customer']['email'],
                'phone' => $paymentDetails['metadata']['phone'],
                'amount' => $paymentDetails['amount'] / 100,
                'reference' => $paymentDetails['reference']
            ]);
        }

        return redirect()->route('applicant.recuitment-payment')->with('error', 'Payment failed or was cancelled');
    }

    function generateStrongPassword($length = 9, $add_dashes = false, $available_sets = 'luds')
    {
        $sets = array();
        if (strpos($available_sets, 'l') !== false)
            $sets[] = 'abcdefghjkmnpqrstuvwxyz';
        if (strpos($available_sets, 'u') !== false)
            $sets[] = 'ABCDEFGHJKMNPQRSTUVWXYZ';
        if (strpos($available_sets, 'd') !== false)
            $sets[] = '23456789';
        if (strpos($available_sets, 's') !== false)
            $sets[] = '!@#$%&*?';

        $all = '';
        $password = '';
        foreach ($sets as $set) {
            $password .= $set[array_rand(str_split($set))];
            $all .= $set;
        }

        $all = str_split($all);
        for ($i = 0; $i < $length - count($sets); $i++)
            $password .= $all[array_rand($all)];

        $password = str_shuffle($password);

        if (!$add_dashes)
            return $password;

        $dash_len = floor(sqrt($length));
        $dash_str = '';
        while (strlen($password) > $dash_len) {
            $dash_str .= substr($password, 0, $dash_len) . '-';
            $password = substr($password, $dash_len);
        }
        $dash_str .= $password;
        return $dash_str;
    }

}
