<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Applicant;
// use App\Models\Applicant;

class PaymentForm extends Component
{
    public $applicantId;
    public function mount($id)
    {
        $this->applicantId = $id;

        // $this->applicant = Applicant::select('id','lead_id','applicant_code','payment_state')->where('applicant_code',  $applicant_code)->first();
    }

    public function render()
    {
        $applicant = Applicant::where('id', $this->applicantId)->first();
        return view('livewire.payment-form', ['applicant' => $applicant]);
    }
}
