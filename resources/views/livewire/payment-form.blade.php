<div class="container d-flex align-items-center justify-content-center">
    <div class="col-xl-5 col-md-12 py-3 py-xl-0">
        <h1 class="text-center  mb-4">PORTAL GH JOBS ONBOARDING</h1>
        <div class="card border-0 rounded-4 shadow-lg bg-gradient-blue-indigo text-white h-100">
            <div class="card-body fs-15px p-30px h-100 d-flex flex-column">
                <div class="d-flex align-items-center">
                    <div class="flex-1">
                        <div class="h1 font-monospace text-white text-center">RECRUITMENT PAYMENT</div>
                        <p class="font-monospace text-white text-center">You are to make recruitemtn payment to contiue
                        </p>
                        <hr>
                        <div class="display-6 fw-bold mb-0 text-white"><span class="text-black">AMOUNT: </span>
                            GHC2,000.00</div>
                    </div>
                    <div>
                        <span class="iconify display-3 text-black text-opacity-50 rounded-3"
                            data-icon="solar:cup-first-bold-duotone"></span>
                    </div>
                </div>

                <hr class="my-4" />
                <form action="{{ route('applicant.payment.initialize') }}" method="POST">
                    @csrf
                    <div class="mb-3">
                        <div class="d-flex">
                            <label class="form-label">Full Name</label>
                        </div>
                        <input type="text" class="form-control form-control-lg fs-15px"
                            name="name"
                            value="{{ ucwords($applicant->fullname) }}"
                            placeholder="{{ ucwords($applicant->fullname) }}" readonly>
                        @error('full_name')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Email Address</label>
                        <input type="email" class="form-control form-control-lg fs-15px"
                            name="email"
                            value="{{ $applicant->email }}" placeholder="{{ $applicant->email }}" readonly>
                        @error('email')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="mb-3">
                        <div class="d-flex">
                            <label class="form-label">Phone Number</label>
                        </div>
                        <input type="text" class="form-control form-control-lg fs-15px"
                            name="phone"
                            value="{{ $applicant->phone }}" placeholder="{{ $applicant->phone }}" readonly>
                        @error('phone')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <div class="d-flex">
                            <label class="form-label">Amount</label>
                        </div>
                        <input type="number" class="form-control form-control-lg fs-15px" value="2000"
                            name="amount"  readonly>
                        @error('amount')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="mb-3">
                        <input type="password" class="form-control form-control-lg fs-15px" name=code value="{{ $applicant->applicant_code }}" hidden>
                    </div>

                    <button type="submit" class="btn btn-light btn-lg w-100 text-black font-monospace mt-3">Proceed to Payment
                        <i class="fa fa-arrow-right"></i></button>
                </form>
            </div>
        </div>
    </div>
</div>
