    <div class="login-content">
        <form wire:submit.prevent="submit">
            <h1 class="text-center">PORTAL GH JOBS ONBOARD</h1>
            <div class="text-muted text-center mb-4">
                Submit your applicant code to continue
            </div>
            <div>



            </div>
            <div class="mb-3">
                <div class="d-flex">
                    <label class="form-label">Applicant Code</label>
                </div>
                @if (session()->has('message'))
                    <div class="alert alert-danger d-flex align-items-center" role="alert">
                        <i class="fa fa-exclamation-triangle me-2"></i>
                            {{ session('message') }}
                    </div>
                @endif
                @if (session()->has('success'))
                    <div class="alert alert-success " role="alert">
                        <h4 class="alert-heading">
                        Congratulations!</h4>
                        <hr>
                        <p class="mb-0">{{ session('success') }}</p>
                    </div>
                @endif
                <input type="text" class="form-control form-control-lg fs-15px" wire:model="applicant_code"
                    placeholder="Enter Applicant Code">
                @error('applicant_code')
                    <span class="text-danger">{{ $message }}</span>
                @enderror
            </div>

            <button type="submit" class="btn btn-theme btn-lg d-block w-100 fw-500 mb-3">Submit</button>
             <div class="text-center text-muted">
						Don't have an applicant code yet?  <a href="{{ URL('/') }}">Sign Up</a>
					</div>
        </form>
    </div>

