&lt;!-- required js / css --&gt;
&lt;link href="/assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.min.css" rel="stylesheet"&gt;
&lt;script src="/assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.min.js"&gt;&lt;/script&gt;

&lt;!-- default html --&gt;
&lt;input type="text" class="form-control" id="datepicker" placeholder="dd/mm/yyyy"&gt;

&lt;!--component html --&gt;
&lt;div class="input-group"&gt;
  &lt;input type="text" class="form-control" id="datepicker" placeholder="with input group addon"&gt;
  &lt;label class="input-group-text" for="datepicker"&gt;
    &lt;i class="fa fa-calendar"&gt;&lt;/i&gt;
  &lt;/label&gt;
&lt;/div&gt;

&lt;!-- range html --&gt;
&lt;div class="input-group input-daterange" id="datepicker"&gt;
  &lt;input type="text" class="form-control" name="start" placeholder="start date"&gt;
  &lt;span class="input-group-text"&gt;to&lt;/span&gt;
  &lt;input type="text" class="form-control" name="end" placeholder="end date"&gt;
&lt;/div&gt;

&lt;!-- script --&gt;
&lt;script&gt;
  $('#datepicker').datepicker({
    autoclose: true
  });
&lt;/script&gt;