&lt;!-- required js --&gt;
&lt;script src="https://maps.googleapis.com/maps/api/js?v=3.exp&amp;sensor=false"&gt;&lt;/script&gt;;

&lt;!-- html --&gt;
&lt;div id="googleMapDefault" style="height: 300px;"&gt;&lt;/div&gt;

&lt;!-- script --&gt;
&lt;script&gt;
  var googleMap;

  function initialize() {
    var mapOptions = {
      zoom: 3,
      center: new google.maps.LatLng(24.55, 271.55),
      mapTypeId: google.maps.MapTypeId.ROADMAP,
      disableDefaultUI: true,
    };
    googleMap = new google.maps.Map(document.getElementById('googleMapDefault'), mapOptions);
  }
  google.maps.event.addDomListener(window, 'load', initialize);

  $(window).on('resize', function() {
    google.maps.event.trigger(googleMap, "resize");
  });
&lt;/script&gt;