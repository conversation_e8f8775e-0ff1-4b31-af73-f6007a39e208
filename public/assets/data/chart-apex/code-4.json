&lt;!-- required js --&gt;
&lt;script src="/assets/plugins/apexcharts/dist/apexcharts.min.js"/&gt;&lt;/script&gt;

&lt;!-- html --&gt;
&lt;div id="apexBarChart"&gt;&lt;/div&gt;

&lt;!-- script --&gt;
&lt;script&gt;
  var apexBarChartOptions = {
    chart: {
      height: 350,
      type: 'bar',
    },
    plotOptions: {
      bar: {
        horizontal: true,
        dataLabels: { position: 'top' }
      }  
    },
    dataLabels: {
      enabled: true,
      offsetX: -6,
      style: {
        fontSize: '12px',
        colors: [app.color.white]
      }
    },
    colors: [app.color.warning, app.color.gray500],
    stroke: { show: false },
    series: [
      { data: [44, 55, 41, 64, 22, 43, 21] },
      { data: [53, 32, 33, 52, 13, 44, 32] }
    ],
    xaxis: {
      categories: [2013, 2014, 2015, 2016, 2017, 2018, 2019],
      axisBorder: {
        show: true,
        color: app.color.borderColor,
        height: 1,
        width: '100%',
        offsetX: 0,
        offsetY: -1
      },
      axisTicks: {
        show: true,
        borderType: 'solid',
        color: app.color.borderColor,
        height: 6,
        offsetX: 0,
        offsetY: 0
      }
    }
  };
  var apexBarChart = new ApexCharts(
    document.querySelector('#apexBarChart'),
    apexBarChartOptions
  );
  apexBarChart.render();
&lt;/script&gt;