&lt;!-- required js --&gt;
&lt;script src="/assets/plugins/apexcharts/dist/apexcharts.min.js"/&gt;&lt;/script&gt;

&lt;!-- html --&gt;
&lt;div id="apexBubbleChart"&gt;&lt;/div&gt;

&lt;!-- script --&gt;
&lt;script&gt;
  function generateBubbleChartData(baseval, count, yrange) {
    var i = 0;
    var series = [];
    while (i < count) {
      var x = Math.floor(Math.random() * (750 - 1 + 1)) + 1;;
      var y = Math.floor(Math.random() * (yrange.max - yrange.min + 1)) + yrange.min;
      var z = Math.floor(Math.random() * (75 - 15 + 1)) + 15;

      series.push([x, y, z]);
      baseval += 86400000;
      i++;
    }
    return series;
  }
  var apexBubbleChartOptions = {
    chart: {
      height: 350,
      type: 'bubble',
    },
    dataLabels: { enabled: false },
    colors: [app.color.theme, app.color.teal, 'rgba('+ app.color.bodyColorRgb + ', .5)', app.color.pink],
    series: [
      { name: 'Bubble1', data: generateBubbleChartData(new Date('11 Feb 2023 GMT').getTime(), 20, { min: 10, max: 60 }) },
      { name: 'Bubble2', data: generateBubbleChartData(new Date('11 Feb 2023 GMT').getTime(), 20, { min: 10, max: 60 }) },
      { name: 'Bubble3', data: generateBubbleChartData(new Date('11 Feb 2023 GMT').getTime(), 20, { min: 10, max: 60 }) },
      { name: 'Bubble4', data: generateBubbleChartData(new Date('11 Feb 2023 GMT').getTime(), 20, { min: 10, max: 60 }) }
    ],
    fill: { opacity: 0.8 },
    title: { text: 'Simple Bubble Chart' },
    xaxis: { tickAmount: 12, type: 'category' },
    yaxis: { max: 70 }
  }
  var apexBubbleChart = new ApexCharts(
    document.querySelector('#apexBubbleChart'),
    apexBubbleChartOptions
  );
  apexBubbleChart.render();
&lt;/script&gt;