&lt;!-- required js --&gt;
&lt;script src="/assets/plugins/chart.js/dist/chart.umd.js"/&gt;&lt;/script&gt;

&lt;!-- html --&gt;
&lt;canvas id="barChart"&gt;&lt;/canvas&gt;

&lt;!-- script --&gt;
&lt;script&gt;
  var ctx2 = document.getElementById('barChart');
  var barChart = new Chart(ctx2, {
    type: 'bar',
    data: {
      labels: ['Jan','Feb','Mar','Apr','May','Jun'],
      datasets: [{
        label: 'Total Visitors',
        data: [37,31,36,34,43,31],
        backgroundColor: 'rgba('+ app.color.themeRgb +', .25)',
        borderColor: app.color.theme,
        borderWidth: 1.5
      },{
        label: 'New Visitors',
        data: [12,16,20,14,23,21],
        backgroundColor: 'rgba('+ app.color.secondaryRgb +', .25)',
        borderColor: app.color.secondary,
        borderWidth: 1.5
      }]
    }
  });
&lt;/script&gt;