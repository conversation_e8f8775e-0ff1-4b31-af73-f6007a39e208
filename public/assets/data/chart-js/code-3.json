&lt;!-- required js --&gt;
&lt;script src="/assets/plugins/chart.js/dist/chart.umd.js"/&gt;&lt;/script&gt;

&lt;!-- html --&gt;
&lt;canvas id="radarChart"&gt;&lt;/canvas&gt;

&lt;!-- script --&gt;
&lt;script&gt;
  var ctx3 = document.getElementById('radarChart');
  var radarChart = new Chart(ctx3, {
    type: 'radar',
    data: {
      labels: ['United States', 'Canada', 'Australia', 'Netherlands', 'Germany', 'New Zealand', 'Singapore'],
      datasets: [
        {
          label: 'Mobile',
          backgroundColor: 'rgba('+ app.color.themeRgb +', .25)',
          borderColor: app.color.theme,
          pointBackgroundColor: app.color.theme,
          pointBorderColor: app.color.theme,
          pointHoverBackgroundColor: app.color.theme,
          pointHoverBorderColor: app.color.theme,
          data: [65, 59, 90, 81, 56, 55, 40],
          borderWidth: 1.5
        },
        {
          label: 'Desktop',
          backgroundColor: 'rgba('+ app.color.secondaryRgb +', .25)',
          borderColor: app.color.secondary,
          pointBackgroundColor: app.color.secondary,
          pointBorderColor: app.color.secondary,
          pointHoverBackgroundColor: app.color.secondary,
          pointHoverBorderColor: app.color.secondary,
          data: [28, 48, 40, 19, 96, 27, 100],
          borderWidth: 1.5
        }
      ]
    }
  });
&lt;/script&gt;