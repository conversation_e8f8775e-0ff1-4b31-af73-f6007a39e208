&lt;!-- required js --&gt;
&lt;script src="/assets/plugins/chart.js/dist/chart.umd.js"/&gt;&lt;/script&gt;

&lt;!-- html --&gt;
&lt;canvas id="doughnutChart"&gt;&lt;/canvas&gt;

&lt;!-- script --&gt;
&lt;script&gt;
  var ctx6 = document.getElementById('doughnutChart');
  var doughnutChart = new Chart(ctx6, {
    type: 'doughnut',
    data: {
      labels: ['Total Visitor', 'New Visitor', 'Returning Visitor'],
      datasets: [{
        data: [300, 50, 100],
        backgroundColor: ['rgba('+ app.color.themeRgb +', .75)', 'rgba('+ app.color.themeRgb +', .25)', 'rgba('+ app.color.themeRgb +', .5)'],
        hoverBackgroundColor: [app.color.theme, app.color.theme, app.color.theme],
        borderWidth: 0
      }]
    }
  });
&lt;/script&gt;