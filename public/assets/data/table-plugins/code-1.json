&lt;!-- required css --&gt;
&lt;link href="/assets/plugins/datatables.net-bs5/css/dataTables.bootstrap5.min.css" rel="stylesheet"&gt;
&lt;link href="/assets/plugins/datatables.net-responsive-bs5/css/responsive.bootstrap5.min.css" rel="stylesheet"&gt;
&lt;link href="/assets/plugins/datatables.net-buttons-bs5/css/buttons.bootstrap5.min.css" rel="stylesheet"&gt;

&lt;!-- required js --&gt;
&lt;script src="/assets/plugins/datatables.net/js/dataTables.min.js"&gt;&lt;/script&gt;
&lt;script src="/assets/plugins/datatables.net-bs5/js/dataTables.bootstrap5.min.js"&gt;&lt;/script&gt;
&lt;script src="/assets/plugins/datatables.net-buttons/js/dataTables.buttons.min.js"&gt;&lt;/script&gt;
&lt;script src="/assets/plugins/datatables.net-buttons/js/buttons.colVis.min.js"&gt;&lt;/script&gt;
&lt;script src="/assets/plugins/datatables.net-buttons/js/buttons.html5.min.js"&gt;&lt;/script&gt;
&lt;script src="/assets/plugins/datatables.net-buttons/js/buttons.print.min.js"&gt;&lt;/script&gt;
&lt;script src="/assets/plugins/datatables.net-buttons-bs5/js/buttons.bootstrap5.min.js"&gt;&lt;/script&gt;
&lt;script src="/assets/plugins/datatables.net-responsive/js/dataTables.responsive.min.js"&gt;&lt;/script&gt;
&lt;script src="/assets/plugins/datatables.net-responsive-bs5/js/responsive.bootstrap5.min.js"&gt;&lt;/script&gt;

&lt;!-- html --&gt;
&lt;table id="datatableDefault" class="table text-nowrap w-100"&gt;
  ...
&lt;/table&gt;

&lt;!-- script --&gt;
&lt;script&gt;
  $('#datatableDefault').DataTable({
    dom: "&lt;'row mb-3'&lt;'col-sm-4'l&gt;&lt;'col-sm-8 text-end'&lt;'d-flex justify-content-end'fB&gt;&gt;&gt;t&lt;'d-flex align-items-center mt-3'&lt;'me-auto'i&gt;&lt;'mb-0'p&gt;&gt;",
    lengthMenu: [ 10, 20, 30, 40, 50 ],
    responsive: true,
    buttons: [
      { extend: 'print', className: 'btn btn-default' },
      { extend: 'csv', className: 'btn btn-default' }
    ]
  });
&lt;/script&gt;
